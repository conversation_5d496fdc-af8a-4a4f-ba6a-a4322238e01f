# app/processing/parsers/fb2/fb2_repairer.py

"""
FB2Repairer - компонент для исправления поврежденного XML в FB2 файлах.

Отвечает за предварительную обработку и исправление различных проблем XML:
- Проблемы с namespace и кодировкой
- Невалидные символы в XML
- Несоответствие открывающих/закрывающих тегов
- Неправильная вложенность параграфов
- Невалидные атрибуты
- Структурные проблемы

Используется FB2Parser перед основным парсингом XML.
"""

import io
import logging
import re
from pathlib import Path
from typing import Any, Union

# Импортируем BeautifulSoup для интеллектуального исправления XML
try:
    from bs4 import BeautifulSoup

    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False


class FB2Repairer:
    """Компонент для исправления поврежденного XML в FB2 файлах."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Статистика XML исправлений для отчетности
        self.xml_fixes_stats: dict[str, Any] = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "beautifulsoup_fixes": 0,  # Новое поле для BeautifulSoup исправлений
            "fixes_details": [],
        }

    def reset_xml_fixes_stats(self) -> None:
        """Сбрасывает статистику XML исправлений перед обработкой нового файла."""
        self.xml_fixes_stats = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "beautifulsoup_fixes": 0,  # Новое поле для BeautifulSoup исправлений
            "fixes_details": [],
        }

    def get_xml_fixes_stats(self) -> dict[str, Any]:
        """Возвращает статистику примененных XML исправлений."""
        return self.xml_fixes_stats.copy()

    def repair(self, source: Union[Path, io.BytesIO]) -> bytes:
        """Публичный интерфейс для исправления поврежденного XML.

        Args:
            source: Путь к файлу или поток с XML данными

        Returns:
            Исправленный XML в виде bytes
        """
        self.reset_xml_fixes_stats()
        return self._preprocess_xml(source)

    def _preprocess_xml(self, source: Union[Path, io.BytesIO]) -> bytes:
        """Предварительная обработка XML для исправления проблем с namespace и кодировкой."""
        try:
            # Читаем содержимое
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    content = f.read()
            else:
                source.seek(0)  # Убеждаемся, что читаем с начала
                content = source.read()
                source.seek(0)  # Возвращаем указатель на начало

            # Декодируем в строку для обработки
            try:
                xml_str = content.decode("utf-8")
            except UnicodeDecodeError:
                # Пробуем другие кодировки
                for encoding in ["cp1251", "iso-8859-1", "latin1"]:
                    try:
                        xml_str = content.decode(encoding)
                        self.logger.warning(f"XML декодирован с кодировкой {encoding} вместо UTF-8")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # Если ничего не помогло, используем замену ошибочных символов
                    xml_str = content.decode("utf-8", errors="replace")
                    self.logger.warning("XML декодирован с заменой ошибочных символов")

            # ЭКСПЕРИМЕНТ: ТОЛЬКО BeautifulSoup, все regex отключены
            xml_str = self._fix_with_beautifulsoup(xml_str)

            # ЗАКОММЕНТИРОВАНО: regex-исправления отключены для эксперимента
            # xml_str = self._fix_xml_issues(xml_str)

            return xml_str.encode("utf-8")

        except Exception as e:
            self.logger.error(f"Ошибка предварительной обработки XML: {e}")
            # В случае ошибки возвращаем исходное содержимое
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    return f.read()
            else:
                source.seek(0)
                content = source.read()
                source.seek(0)
                return content

    def _fix_xml_issues(self, xml_str: str) -> str:
        """РАСШИРЕННАЯ обработка XML проблем для поврежденных FB2 файлов."""

        # 1. Исправляем проблемы с namespace
        xml_str = self._fix_namespace_issues(xml_str)

        # 2. Исправляем невалидные символы в XML
        xml_str = self._fix_invalid_xml_chars(xml_str)

        # 3. Исправляем несоответствие тегов
        xml_str = self._fix_mismatched_tags(xml_str)

        # 4. Исправляем невалидные атрибуты
        xml_str = self._fix_invalid_attributes(xml_str)

        # 5. Исправляем структурные проблемы
        xml_str = self._fix_structural_issues(xml_str)

        return xml_str

    def _fix_namespace_issues(self, xml_str: str) -> str:
        """Консервативно исправляет только известные проблемы с namespace в XML."""

        # Исправляем только конкретную проблему: l:href без объявления namespace
        if "l:href=" in xml_str:
            # Находим корневой элемент FictionBook
            root_match = re.search(r"<FictionBook[^>]*>", xml_str)
            if root_match:
                root_tag = root_match.group(0)

                # Проверяем, есть ли уже объявление для префикса 'l'
                if "xmlns:l=" not in root_tag:
                    # Добавляем объявление namespace для префикса 'l'
                    if 'xmlns:xlink="http://www.w3.org/1999/xlink"' in root_tag:
                        # Добавляем рядом с существующим xlink
                        new_root_tag = root_tag.replace(
                            'xmlns:xlink="http://www.w3.org/1999/xlink"',
                            'xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:l="http://www.w3.org/1999/xlink"',
                        )
                    else:
                        # Добавляем оба namespace
                        new_root_tag = root_tag.replace(
                            ">", ' xmlns:l="http://www.w3.org/1999/xlink" xmlns:xlink="http://www.w3.org/1999/xlink">'
                        )

                    xml_str = xml_str.replace(root_tag, new_root_tag)
                    self.logger.warning("Добавлено объявление namespace для префикса 'l:' (исправление l:href)")

        return xml_str

    def _fix_invalid_xml_chars(self, xml_str: str) -> str:
        """Исправляет невалидные символы в XML."""

        # Удаляем управляющие символы, которые недопустимы в XML
        # Оставляем только допустимые: \t (0x09), \n (0x0A), \r (0x0D) и символы >= 0x20
        xml_str = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", xml_str)

        # Исправляем неэкранированные амперсанды (& без ; в конце)
        xml_str = re.sub(r"&(?![a-zA-Z0-9#]+;)", "&amp;", xml_str)

        # Исправляем неэкранированные < и > в текстовом содержимом
        # Это сложная задача, поэтому делаем консервативно

        return xml_str

    def _fix_mismatched_tags(self, xml_str: str) -> str:
        """Исправляет несоответствие открывающих/закрывающих тегов с умной логикой."""

        # 1. Исправляем конкретные паттерны неправильной вложенности параграфов
        xml_str = self._fix_paragraph_nesting(xml_str)

        # 2. Простая проверка баланса тегов для основных FB2 элементов
        fb2_tags = ["p", "strong", "emphasis", "section", "title", "subtitle"]

        for tag_name in fb2_tags:
            # Подсчитываем открывающие и закрывающие теги
            open_count = len(re.findall(f"<{tag_name}[^/>]*(?<!/)>", xml_str))
            close_count = len(re.findall(f"</{tag_name}>", xml_str))

            # Если закрывающих больше, удаляем лишние
            if close_count > open_count:
                excess = close_count - open_count
                for _ in range(excess):
                    # Удаляем последний лишний закрывающий тег
                    xml_str = re.sub(f"</{tag_name}>", "", xml_str, count=1)

                self.logger.warning(f"Удалено {excess} лишних закрывающих тегов </{tag_name}>")

        return xml_str

    def _fix_paragraph_nesting(self, xml_str: str) -> str:
        """Исправляет специфичные проблемы с вложенностью параграфов.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенные regex операции для максимальной производительности.
        """

        # Счетчики для логирования
        fixes_count = {
            "nested_paragraphs": 0,
            "empty_paragraphs": 0,
            "multiple_opening": 0,
            "emphasis_quotes": 0,
            "orphan_quotes": 0,
            "missing_p_tags": 0,
        }

        # ОПТИМИЗАЦИЯ 1: Объединенные regex операции в один проход
        # Определяем все паттерны для замены
        replacement_patterns = [
            # Исправляем паттерн: </p><p>...много контента...</p></p>
            (r"</p><p>(.*?)</p></p>", r"<p>\1</p>", "nested_paragraphs"),
            # Исправляем самозакрывающиеся параграфы <p/> -> удаляем их
            (r"<p\s*/>", "", "empty_paragraphs"),
            # Исправляем множественные открывающие теги <p><p><p>
            (r"(<p[^>]*>)\s*(<p[^>]*>)+", r"\1", "multiple_opening"),
            # Исправляем одиночные закрывающие теги </p> на отдельных строках
            ##(r"^\s*</p>\s*$", "", "orphan_close_p"),
            # Исправляем одиночные закрывающие теги </section> без соответствующих открывающих
            ##(r"^\s*</section>\s*$", "", "orphan_close_section"),
            # Исправляем проблемы с emphasis внутри параграфов
            (r'<emphasis>([^<]*?)([»"])</emphasis></p>', r"<emphasis>\1</emphasis>\2</p>", "emphasis_quotes"),
            # Исправляем проблему с лишними кавычками после emphasis
            (r'</emphasis>([»"])</p>', r"</emphasis></p>", "orphan_quotes"),
        ]

        # Применяем все паттерны в едином цикле
        for pattern, replacement, fix_type in replacement_patterns:
            flags = re.DOTALL | re.MULTILINE if fix_type in ["orphan_close_p", "orphan_close_section"] else re.DOTALL

            # Подсчитываем количество совпадений перед заменой
            matches = re.findall(pattern, xml_str, flags=flags)
            if matches:
                fixes_count[fix_type] = len(matches)
                xml_str = re.sub(pattern, replacement, xml_str, flags=flags)

        # ОПТИМИЗАЦИЯ 2: Замена построчной обработки на regex
        # Исправляем проблему с отсутствующими открывающими <p> тегами
        # Паттерн: строка заканчивается на </emphasis></p>, но не начинается с <p>
        missing_p_pattern = r"^(\s*)(?!<p>)([^<\n]*<emphasis>.*?</emphasis></p>)$"
        missing_p_matches = re.findall(missing_p_pattern, xml_str, re.MULTILINE)

        if missing_p_matches:
            # Проверяем контекст - не является ли это продолжением предыдущей строки
            def replace_missing_p(match):
                indent, content = match.groups()
                # Простая эвристика: если строка начинается с заглавной буквы или кавычки, добавляем <p>
                if content.strip() and (content.strip()[0].isupper() or content.strip()[0] in '«"'):
                    return f"{indent}<p>{content}"
                return f"{indent}{content}"

            xml_str = re.sub(missing_p_pattern, replace_missing_p, xml_str, flags=re.MULTILINE)
            fixes_count["missing_p_tags"] = len(missing_p_matches)

        # Логирование результатов и сбор статистики
        total_paragraph_fixes = 0
        for fix_type, count in fixes_count.items():
            if count > 0:
                total_paragraph_fixes += count
                fix_messages = {
                    "nested_paragraphs": f"Исправлено {count} случаев неправильной вложенности параграфов",
                    "empty_paragraphs": f"Удалено {count} пустых самозакрывающихся параграфов <p/>",
                    "multiple_opening": f"Исправлено {count} случаев множественных открывающих тегов <p>",
                    "emphasis_quotes": f"Исправлено {count} случаев кавычек внутри emphasis",
                    "orphan_quotes": f"Удалено {count} лишних кавычек после emphasis",
                    "missing_p_tags": f"Добавлено {count} отсутствующих открывающих тегов <p>",
                }
                self.logger.warning(fix_messages[fix_type])

                # Сохраняем детали для статистики
                self.xml_fixes_stats["fixes_details"].append(
                    {"type": "paragraph", "subtype": fix_type, "count": count, "message": fix_messages[fix_type]}
                )

        # Обновляем общую статистику
        if total_paragraph_fixes > 0:
            self.xml_fixes_stats["paragraph_fixes"] += total_paragraph_fixes
            self.xml_fixes_stats["total_fixes_applied"] += total_paragraph_fixes

        return xml_str

    def _fix_invalid_attributes(self, xml_str: str) -> str:
        """Исправляет невалидные атрибуты в тегах.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенная обработка URL и атрибутов.
        """

        # ОПТИМИЗАЦИЯ: Объединенная обработка всех проблем с src-url в один проход
        src_url_patterns = [
            # Исправляем конкретную проблему: art="число</src-url">
            (r'(\?art=)"(\d+)(</src-url>)">', r"\1\2\3>"),
            # Исправляем другие варианты проблемных кавычек в src-url
            (r'(\?art=)"(\d+)(?=</src-url>)', r"\1\2"),
            # Исправляем лишние кавычки после закрывающих тегов
            (r'(</src-url)">', r"\1>"),
        ]

        src_url_fixes = 0
        for pattern, replacement in src_url_patterns:
            matches = re.findall(pattern, xml_str)
            if matches:
                xml_str = re.sub(pattern, replacement, xml_str)
                src_url_fixes += len(matches)

        if src_url_fixes > 0:
            message = f"Исправлено {src_url_fixes} проблемных кавычек в src-url"
            self.logger.warning(message)

            # Сохраняем детали для статистики
            self.xml_fixes_stats["fixes_details"].append(
                {"type": "attribute", "subtype": "src_url_quotes", "count": src_url_fixes, "message": message}
            )

            # Обновляем общую статистику
            self.xml_fixes_stats["attribute_fixes"] += src_url_fixes
            self.xml_fixes_stats["total_fixes_applied"] += src_url_fixes

        # ОПТИМИЗАЦИЯ: Улучшенная защита URL с кэшированием
        url_placeholders = {}
        url_counter = 0

        def protect_url(match):
            nonlocal url_counter
            placeholder = f"__URL_PLACEHOLDER_{url_counter}__"
            url_placeholders[placeholder] = match.group(0)
            url_counter += 1
            return placeholder

        # Защищаем содержимое src-url тегов
        xml_str = re.sub(r"<src-url>([^<]+)</src-url>", protect_url, xml_str)

        # Исправляем атрибуты без кавычек
        xml_str = re.sub(r'(\w+)=([^"\s>]+)(?=\s|>)', r'\1="\2"', xml_str)

        # Восстанавливаем URL (оптимизированная версия)
        if url_placeholders:
            for placeholder, original in url_placeholders.items():
                xml_str = xml_str.replace(placeholder, original)

        # ОПТИМИЗАЦИЯ: Улучшенное удаление дублирующихся атрибутов
        # Используем более эффективный подход с предварительной проверкой
        if "=" in xml_str and '"' in xml_str:  # Быстрая проверка наличия атрибутов

            def remove_duplicate_attrs(match):
                tag_content = match.group(1)

                # Быстрая проверка - есть ли дублирующиеся атрибуты
                if tag_content.count("=") <= 1:
                    return match.group(0)  # Нет смысла обрабатывать

                attrs = {}
                attr_pattern = r'(\w+)="([^"]*)"'

                def replace_attr(attr_match):
                    attr_name, attr_value = attr_match.groups()
                    if attr_name not in attrs:
                        attrs[attr_name] = attr_value
                        return attr_match.group(0)
                    return ""  # Удаляем дубликат

                cleaned_content = re.sub(attr_pattern, replace_attr, tag_content)
                return f"<{cleaned_content}>"

            xml_str = re.sub(r"<([^>]+)>", remove_duplicate_attrs, xml_str)

        return xml_str

    def _fix_structural_issues(self, xml_str: str) -> str:
        """Исправляет структурные проблемы в FB2 файлах.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенный анализ тегов и эффективное удаление.
        """

        # ОПТИМИЗАЦИЯ: Объединенный анализ всех структурных тегов в один проход
        structural_tags = {
            "body": (r"<body[^>]*>", r"</body>", 7),
            "FictionBook": (r"<FictionBook[^>]*>", r"</FictionBook>", 14),
            "image": (r"<image[^/>]*(?<!/)>", r"</image>", 8),
            "empty-line": (r"<empty-line[^/>]*(?<!/)>", r"</empty-line>", 13),
            "subtitle": (r"<subtitle[^/>]*(?<!/)>", r"</subtitle>", 11),
            "cite": (r"<cite[^/>]*(?<!/)>", r"</cite>", 7),
        }

        fixes_applied = {}

        # Анализируем все теги в едином цикле
        for tag_name, (open_pattern, close_pattern, close_tag_len) in structural_tags.items():
            # Подсчитываем открывающие и закрывающие теги
            open_matches = re.findall(open_pattern, xml_str)
            close_matches = re.findall(close_pattern, xml_str)

            open_count = len(open_matches)
            close_count = len(close_matches)

            if close_count > open_count:
                excess = close_count - open_count

                # ОПТИМИЗАЦИЯ: Удаляем лишние теги за один проход с конца
                # Находим все позиции закрывающих тегов
                close_positions = []
                start_pos = 0
                while True:
                    pos = xml_str.find(close_pattern.replace(r"</", "</"), start_pos)
                    if pos == -1:
                        break
                    close_positions.append(pos)
                    start_pos = pos + 1

                # Удаляем лишние теги с конца (сохраняем порядок)
                if close_positions and excess > 0:
                    # Сортируем позиции по убыванию для удаления с конца
                    close_positions.sort(reverse=True)

                    removed_count = 0
                    for pos in close_positions[:excess]:
                        # Проверяем, что тег еще существует на этой позиции
                        close_tag = f"</{tag_name}>"
                        if xml_str[pos : pos + close_tag_len] == close_tag:
                            xml_str = xml_str[:pos] + xml_str[pos + close_tag_len :]
                            removed_count += 1
                            if removed_count >= excess:
                                break

                    if removed_count > 0:
                        fixes_applied[tag_name] = removed_count

        # Логирование результатов и сбор статистики
        if fixes_applied:
            total_structural_fixes = sum(fixes_applied.values())

            # Отдельное логирование для основных тегов
            for tag_name in ["body", "FictionBook"]:
                if tag_name in fixes_applied:
                    message = f"Удалено {fixes_applied[tag_name]} лишних закрывающих тегов </{tag_name}>"
                    self.logger.warning(message)

                    # Сохраняем детали для статистики
                    self.xml_fixes_stats["fixes_details"].append(
                        {
                            "type": "structural",
                            "subtype": f"{tag_name}_tags",
                            "count": fixes_applied[tag_name],
                            "message": message,
                        }
                    )

            # Суммарное логирование для остальных тегов
            other_tags_total = sum(count for tag, count in fixes_applied.items() if tag not in ["body", "FictionBook"])
            if other_tags_total > 0:
                message = f"Удалено {other_tags_total} лишних закрывающих тегов различных типов"
                self.logger.warning(message)

                # Сохраняем детали для статистики
                self.xml_fixes_stats["fixes_details"].append(
                    {"type": "structural", "subtype": "other_tags", "count": other_tags_total, "message": message}
                )

            # Обновляем общую статистику
            self.xml_fixes_stats["structural_fixes"] += total_structural_fixes
            self.xml_fixes_stats["total_fixes_applied"] += total_structural_fixes

        return xml_str

    def _fix_with_beautifulsoup(self, xml_str: str) -> str:
        """Интеллектуальное исправление XML через BeautifulSoup с lxml-xml парсером.

        Этот метод применяется ПЕРВЫМ на чистом исходном XML для структурной нормализации.
        BeautifulSoup с lxml-xml парсером может исправить:
        - Неправильную вложенность тегов
        - Некорректные атрибуты без кавычек
        - Дублирующиеся атрибуты
        - Структурные проблемы, которые regex не может обработать

        Args:
            xml_str: Чистая исходная XML строка (до regex-исправлений)

        Returns:
            Структурно нормализованная XML строка
        """
        if not BS4_AVAILABLE:
            self.logger.debug("BeautifulSoup недоступен, пропускаем этап интеллектуального исправления")
            return xml_str

        # BeautifulSoup применяется ВСЕГДА когда вызывается FB2Repairer
        # Это нужно для нормализации структуры секций, body и улучшения разделения на главы

        try:
            # Сохраняем исходную длину для сравнения
            original_length = len(xml_str)

            # Парсим XML через BeautifulSoup с lxml-xml парсером
            # lxml-xml более строгий к XML, чем html.parser, но более толерантный чем стандартные парсеры
            soup = BeautifulSoup(xml_str, features="lxml-xml")

            # Получаем нормализованный XML
            normalized_xml = str(soup)

            # Проверяем, произошли ли изменения
            if len(normalized_xml) != original_length or normalized_xml != xml_str:
                # Подсчитываем количество исправлений (приблизительно)
                fixes_count = 1  # Базовое исправление структуры

                # Дополнительные эвристики для подсчета исправлений
                if normalized_xml.count("<") != xml_str.count("<"):
                    fixes_count += abs(normalized_xml.count("<") - xml_str.count("<"))

                if normalized_xml.count('"') != xml_str.count('"'):
                    fixes_count += abs(normalized_xml.count('"') - xml_str.count('"')) // 2

                message = f"BeautifulSoup нормализовал XML структуру ({fixes_count} исправлений)"
                self.logger.warning(message)

                # Сохраняем детали для статистики
                self.xml_fixes_stats["fixes_details"].append(
                    {
                        "type": "beautifulsoup",
                        "subtype": "structural_normalization",
                        "count": fixes_count,
                        "message": message,
                    }
                )

                # Обновляем общую статистику
                self.xml_fixes_stats["beautifulsoup_fixes"] += fixes_count
                self.xml_fixes_stats["total_fixes_applied"] += fixes_count

                return normalized_xml
            else:
                self.logger.debug("BeautifulSoup: XML уже корректен, изменения не требуются")
                return xml_str

        except Exception as e:
            # Если BeautifulSoup не может обработать XML, возвращаем исходную строку
            self.logger.warning(f"BeautifulSoup не смог обработать XML: {e}. Возвращаем исходный XML")
            return xml_str
