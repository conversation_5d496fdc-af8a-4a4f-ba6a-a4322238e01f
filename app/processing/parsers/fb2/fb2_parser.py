# app/processing/parsers/fb2/fb2_parser.py

"""
🚨 ВАЖНО: FB2 парсер УЖЕ ПОЛНОСТЬЮ РЕАЛИЗОВАН И РАСШИРЕН!

FB2Parser - высокопроизводительный XML парсер FB2 формата в типизированную модель.
- Использует lxml для максимальной производительности и устойчивости к ошибкам
- Обрабатывает все элементы FB2: метаданные, главы
- Очищает XML namespaces
- РАСШИРЕННАЯ обработка параграфов с полной поддержкой вложенности
- Поддерживает все FB2 элементы форматирования:
  * Базовое: strong, emphasis, комбинированное bold+italic
  * Специальное: sub, sup, strikethrough, code, style
  * Ссылки и сноски: a[type="note"], внешние ссылки
  * Сложное вложение: любые комбинации элементов
- МНОГОУРОВНЕВАЯ система извлечения сносок:
  * Стандартная: <body name="notes"> с секциями
  * По заголовку: секции "Примечания/Notes/Сноски"
  * По паттерну ID: любые секции с id="n_X", "note_X", "fn_X"
  * По последовательности: номера в <strong> после заголовка "notes"
- Возвращает строго типизированную FB2Book модель

Используется совместно с FB2CanonicalTransformer для получения CanonicalBook.

РЕФАКТОРИНГ: Разделен на специализированные компоненты:
- FB2Repairer: исправление поврежденного XML
- FootnoteExtractor: извлечение сносок
- FB2Parser: основная логика парсинга валидного XML

См. doc/processing_parsers.md для деталей.
"""

import base64
import io  # Добавлен для поддержки io.BytesIO
import logging
import re
from pathlib import Path
from typing import Any, Optional, Union

# Используем lxml для значительно большей производительности и устойчивости к ошибкам
try:
    from lxml import etree as ET
except ImportError:
    # Fallback на defusedxml для безопасности (не должно происходить в production)
    import defusedxml.ElementTree as ET

# ИСПРАВЛЕНИЕ: Используем только lxml для совместимости типов
# import defusedxml.ElementTree as ET_safe  # Больше не нужен

from ...error_handler import QuarantineError
from .fb2_model import *
from .fb2_model import ParagraphContent
from .fb2_repairer import FB2Repairer
from .footnote_extractor import FootnoteExtractor

# Регулярное выражение для очистки namespace из тегов
# Пример: {http://www.gribuser.ru/xml/fictionbook/2.0}p -> p
NS_CLEANUP_REGEX = re.compile(r"\{.*\}")


class FB2Parser:
    """Парсер для формата FB2, который преобразует XML-файл
    в строго типизированную модель FB2Book.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.binaries: dict[str, BinaryData] = {}

        # Компоненты для исправления XML и извлечения сносок
        self._repairer = FB2Repairer()
        self._footnote_extractor = FootnoteExtractor()

    def reset_xml_fixes_stats(self) -> None:
        """Сбрасывает статистику XML исправлений перед парсингом нового файла."""
        self._repairer.reset_xml_fixes_stats()

    def get_xml_fixes_stats(self) -> dict[str, Any]:
        """Возвращает статистику примененных XML исправлений."""
        return self._repairer.get_xml_fixes_stats()

    def parse(self, source: Union[Path, io.BytesIO]) -> FB2Book:
        """Основной метод для парсинга FB2 файла.

        Args:
            source: Путь к FB2 файлу или поток с данными.

        Returns:
            Экземпляр FB2Book с данными из файла.

        Raises:
            QuarantineError: Если файл поврежден или не является FB2.

        """
        try:
            # Определяем имя источника для логирования
            filename = source.name if isinstance(source, Path) else getattr(source, "name", "<stream>")

            self.logger.info(f"Начинаем парсинг FB2: {filename}")
            self.binaries = {}  # Сбрасываем для каждого файла
            self.reset_xml_fixes_stats()  # Сбрасываем статистику исправлений

            # ИСПРАВЛЕНИЕ: Используем lxml для совместимости с FootnoteExtractor
            try:
                if isinstance(source, Path):
                    with open(source, "rb") as f:
                        content = f.read()
                else:
                    source.seek(0)
                    content = source.read()
                    source.seek(0)

                # Парсим через lxml
                root = ET.fromstring(content)

                # Очищаем namespace в тегах
                for elem in root.iter():
                    # ИСПРАВЛЕНИЕ: Пропускаем комментарии и другие специальные элементы
                    if hasattr(elem, 'tag') and elem.tag is not None:
                        try:
                            # Конвертируем tag в строку для совместимости с cython
                            elem.tag = NS_CLEANUP_REGEX.sub("", str(elem.tag))
                        except (AttributeError, TypeError):
                            # Пропускаем элементы с read-only tag (комментарии, PI и т.д.)
                            continue

            except Exception as e:
                # Проверяем, является ли это ошибкой парсинга XML
                error_str = str(e).lower()
                error_type = str(type(e))

                xml_parse_errors = [
                    "unbound prefix",
                    "parseerror",
                    "not well-formed",
                    "mismatched tag",
                    "invalid token",
                    "xmlsyntaxerror",
                ]

                is_xml_error = any(
                    error_pattern in error_str or error_pattern in error_type.lower()
                    for error_pattern in xml_parse_errors
                )

                if is_xml_error:
                    # ЭКСПЕРИМЕНТ: FB2Repairer с ТОЛЬКО BeautifulSoup
                    self.logger.warning(f"Обнаружена проблема с XML, применяем ТОЛЬКО BeautifulSoup: {e}")

                    try:
                        xml_content = self._repairer.repair(source)
                        # ИСПРАВЛЕНИЕ: Используем lxml вместо ElementTree для совместимости
                        root = ET.fromstring(xml_content)

                        # Очищаем namespace в тегах
                        for elem in root.iter():
                            # ИСПРАВЛЕНИЕ: Пропускаем комментарии и другие специальные элементы
                            if hasattr(elem, 'tag') and elem.tag is not None:
                                try:
                                    # Конвертируем tag в строку для совместимости с cython
                                    elem.tag = NS_CLEANUP_REGEX.sub("", str(elem.tag))
                                except (AttributeError, TypeError):
                                    # Пропускаем элементы с read-only tag (комментарии, PI и т.д.)
                                    continue

                        self.logger.info("XML исправлен через BeautifulSoup и распарсен")

                    except Exception as repair_error:
                        # Если BeautifulSoup не помог, пробуем lxml recover=True
                        self.logger.warning(f"BeautifulSoup не помог: {repair_error}. Пробуем lxml с recover=True")

                        try:
                            # Используем lxml с режимом восстановления
                            from lxml import etree

                            if isinstance(source, Path):
                                with open(source, "rb") as f:
                                    content = f.read()
                            else:
                                source.seek(0)
                                content = source.read()
                                source.seek(0)

                            # Парсим с режимом восстановления
                            parser = etree.XMLParser(recover=True, encoding="utf-8")
                            root = etree.fromstring(content, parser)

                            # Очищаем namespace в тегах
                            for elem in root.iter():
                                # ИСПРАВЛЕНИЕ: Пропускаем комментарии и другие специальные элементы
                                if hasattr(elem, 'tag') and elem.tag is not None:
                                    try:
                                        # Конвертируем tag в строку для совместимости с cython
                                        elem.tag = NS_CLEANUP_REGEX.sub("", str(elem.tag))
                                    except (AttributeError, TypeError):
                                        # Пропускаем элементы с read-only tag (комментарии, PI и т.д.)
                                        continue

                            self.logger.info("XML восстановлен с помощью lxml recover=True")

                        except Exception as lxml_error:
                            self.logger.error(f"Все попытки восстановления XML провалились: {lxml_error}")
                            raise QuarantineError(f"Невозможно распарсить поврежденный XML: {e}") from e
                else:
                    raise

            # 1. Парсим метаданные (<description>)
            description_elem = root.find("description")
            if description_elem is None:
                raise QuarantineError("Отсутствует обязательный тег <description> в FB2 файле.")
            description = self._parse_description(description_elem)

            # 2. Парсим тело книги (<body>), ИСКЛЮЧАЯ блоки сносок
            # Исключаем все варианты: 'notes', 'footnotes'
            footnote_body_names = {"notes", "footnotes"}
            bodies = [self._parse_body(b) for b in root.findall("body") if b.get("name") not in footnote_body_names]

            # 3. Извлекаем сноски из <body name="notes">
            footnotes = self._footnote_extractor.extract(root)

            return FB2Book(
                description=description,
                bodies=bodies,
                binaries=self.binaries,
                footnotes=footnotes,
            )

        except (ET.ParseError, getattr(ET, "XMLSyntaxError", ET.ParseError)) as e:
            # lxml может выбрасывать как ParseError так и XMLSyntaxError
            raise QuarantineError(f"Ошибка парсинга XML в источнике {filename}: {e}") from e
        except Exception as e:
            if not isinstance(e, QuarantineError):
                self.logger.error(f"Неожиданная ошибка при парсинге FB2: {e}", exc_info=True)
                raise QuarantineError(f"Не удалось обработать FB2 источник: {e}") from e
            raise

    def _get_text(self, element: ET.Element) -> Optional[str]:
        """Безопасно получает текст элемента."""
        return element.text.strip() if element is not None and element.text else None

    def _get_attr(self, element: ET.Element, attr_name: str) -> Optional[str]:
        """Безопасно получает атрибут элемента, включая варианты с namespace и нестандартные префиксы."""
        if element is None:
            return None

        # Проверяем основной атрибут и стандартные варианты
        standard_variants = [
            attr_name,  # href
            f"{{http://www.w3.org/1999/xlink}}{attr_name}",  # {http://www.w3.org/1999/xlink}href
            f"xlink:{attr_name}",  # xlink:href (если не очищен namespace)
        ]

        for key in standard_variants:
            if key in element.attrib:
                return element.attrib[key]

        # Fallback: ищем любой атрибут, заканчивающийся на attr_name (для нестандартных префиксов)
        # Это поможет обработать случаи типа l:href, link:href и т.д.
        for key in element.attrib:
            if key.endswith(f":{attr_name}") or key.endswith(f"}}{attr_name}"):
                self.logger.warning(f"Найден нестандартный атрибут '{key}' вместо стандартного '{attr_name}'")
                return element.attrib[key]

        return None

    def _parse_binary(self, element: ET.Element):
        """Парсит тег <binary> и сохраняет данные."""
        bin_id = element.get("id")
        content_type = element.get("content-type")
        text = self._get_text(element)
        if not all([bin_id, content_type, text]):
            return  # Пропускаем неполные бинарные теги

        try:
            data = base64.b64decode(text or "")
            self.binaries[bin_id] = BinaryData(id=bin_id, content_type=content_type, data=data)
        except Exception as e:
            self.logger.warning(f"Не удалось декодировать бинарные данные для id='{bin_id}': {e}")

    def _parse_description(self, element: ET.Element) -> Description:
        """Парсит тег <description>."""
        return Description(
            title_info=self._parse_title_info(element.find("title-info")),
            document_info=self._parse_document_info(element.find("document-info")),
            publish_info=self._parse_publish_info(element.find("publish-info")),
        )

    def _parse_title_info(self, element: Optional[ET.Element]) -> Optional[TitleInfo]:
        """Парсит тег <title-info>."""
        if element is None:
            return None
        return TitleInfo(
            genres=[Genre(text=self._get_text(g) or "") for g in element.findall("genre") if self._get_text(g)],
            authors=[self._parse_author(a) for a in element.findall("author")],
            book_title=self._get_text(element.find("book-title")),
            annotation=self._parse_annotation(element.find("annotation")),
            keywords=self._get_text(element.find("keywords")),
            date=self._parse_date(element.find("date")),
            lang=self._get_text(element.find("lang")),
            src_lang=self._get_text(element.find("src-lang")),
            translators=[self._parse_author(a) for a in element.findall("translator")],
            sequences=[self._parse_sequence(s) for s in element.findall("sequence") if s.get("name")],
            coverpage=None,
        )

    def _parse_author(self, element: ET.Element) -> Author:
        """Парсит тег <author> или <translator>."""
        return Author(
            first_name=self._get_text(element.find("first-name")),
            middle_name=self._get_text(element.find("middle-name")),
            last_name=self._get_text(element.find("last-name")),
            nickname=self._get_text(element.find("nickname")),
            email=self._get_text(element.find("email")),
            home_page=self._get_text(element.find("home-page")),
            author_id=self._get_text(element.find("id")),
        )

    def _parse_sequence(self, element: ET.Element) -> SequenceInfo:
        """Парсит тег <sequence>."""
        number_str = element.get("number")
        return SequenceInfo(
            name=element.get("name", ""),
            number=int(number_str) if number_str and number_str.isdigit() else None,
        )

    def _parse_date(self, element: Optional[ET.Element]) -> Optional[DateInfo]:
        """Парсит тег <date>."""
        if element is None:
            return None
        return DateInfo(value=element.get("value"), text=self._get_text(element))

    def _parse_image(self, element: Optional[ET.Element]) -> Optional[Image]:
        """Парсит тег <image>."""
        if element is None:
            return None
        href = self._get_attr(element, "href")
        if href:
            # Убираем '#' из начала href, если он есть
            return Image(href=href.lstrip("#"))
        return None

    def _parse_annotation(self, element: Optional[ET.Element]) -> Optional[Annotation]:
        """Парсит тег <annotation>."""
        if element is None:
            return None

        from .fb2_model import AnnotationElement

        content_list: list[AnnotationElement] = []
        for child in element:
            tag = child.tag
            if tag == "p":
                content_list.append(self._parse_paragraph(child))
            elif tag == "poem":
                content_list.append(self._parse_poem(child))
            elif tag == "cite":
                content_list.append(self._parse_cite(child))
            elif tag == "image":
                # Картинки полностью игнорируем
                continue
            elif tag == "empty-line":
                # Пустые строки игнорируем
                continue
            # Остальные неизвестные теги тоже игнорируем

        return Annotation(elements=content_list)

    def _get_full_text_content(self, element: ET.Element) -> str:
        """Рекурсивно извлекает весь текстовый контент из элемента, включая вложенные теги."""
        text_parts = []

        # Добавляем текст самого элемента
        if element.text:
            text_parts.append(element.text.strip())

        # Рекурсивно обрабатываем всех потомков
        for child in element:
            child_text = self._get_full_text_content(child)
            if child_text:
                text_parts.append(child_text)

            # Добавляем tail текст (текст после закрывающего тега потомка)
            if child.tail:
                text_parts.append(child.tail.strip())

        return " ".join(filter(None, text_parts))

    def _parse_paragraph_content(self, element: ET.Element) -> list[ParagraphContent]:
        """Рекурсивно парсит содержимое элемента, обрабатывая все FB2 теги форматирования."""
        content = []

        # Добавляем текст элемента перед первым дочерним элементом
        if element.text:
            text = element.text.strip()
            if text:
                content.append(text)

        # Рекурсивно обрабатываем каждый дочерний элемент
        for child in element:
            parsed_element = self._parse_format_element(child)
            if parsed_element is not None:
                content.append(parsed_element)

            # Добавляем tail текст (текст после закрывающего тега дочернего элемента)
            if child.tail:
                tail = child.tail.strip()
                if tail:
                    content.append(tail)

        return content

    def _parse_format_element(self, element: ET.Element) -> Optional[ParagraphContent]:
        """Парсит отдельный элемент форматирования с полной поддержкой вложенности."""
        tag = element.tag

        if tag == "strong":
            # Проверяем на вложенные элементы для комбинированного форматирования
            emphasis_child = element.find("emphasis")
            if emphasis_child is not None:
                from .fb2_model import BoldItalic

                # Рекурсивно обрабатываем содержимое emphasis
                # nested_content = self._parse_paragraph_content(emphasis_child)
                nested_content = self._parse_paragraph_content(element)
                text = self._flatten_content_to_text(nested_content)
                return BoldItalic(text=text) if text else None
            else:
                from .fb2_model import Strong

                nested_content = self._parse_paragraph_content(element)
                text = self._flatten_content_to_text(nested_content)
                return Strong(text=text) if text else None

        elif tag == "emphasis":
            # Обрабатываем только простое курсивное форматирование
            # ВАЖНО: Комбинированное форматирование <emphasis><strong> НЕ обрабатывается здесь!
            # Оно обрабатывается в блоке "strong" для единообразия (приоритет у внешнего тега).
            # Исправлено: убрана дублирующая проверка strong_child (недостижимый код).
            from .fb2_model import Emphasis

            nested_content = self._parse_paragraph_content(element)
            text = self._flatten_content_to_text(nested_content)
            return Emphasis(text=text) if text else None

        elif tag == "sub":
            from .fb2_model import Subscript

            text = self._get_full_text_content(element)
            return Subscript(text=text) if text else None

        elif tag == "sup":
            from .fb2_model import Superscript

            text = self._get_full_text_content(element)
            return Superscript(text=text) if text else None

        elif tag == "strikethrough":
            from .fb2_model import Strikethrough

            text = self._get_full_text_content(element)
            return Strikethrough(text=text) if text else None

        elif tag == "code":
            from .fb2_model import Code

            text = self._get_full_text_content(element)
            return Code(text=text) if text else None

        elif tag == "style":
            from .fb2_model import Style

            name = element.get("name")
            text = self._get_full_text_content(element)
            return Style(name=name, text=text) if text else None

        elif tag == "a":
            # Обрабатываем ссылки и сноски
            href = self._get_attr(element, "href")
            link_type = element.get("type")
            text = self._get_full_text_content(element)

            if href:
                if link_type == "note":
                    from .fb2_model import Note

                    return Note(href=href, text=text)
                else:
                    from .fb2_model import Link

                    return Link(href=href, text=text)
        else:
            # Для неизвестных тегов просто извлекаем текст
            text = self._get_full_text_content(element)
            return text if text else None

        return None

    def _flatten_content_to_text(self, content: list[ParagraphContent]) -> str:
        """Преобразует список контента в плоский текст для простых элементов форматирования."""
        text_parts = []
        for item in content:
            if isinstance(item, str):
                text_parts.append(item)
            elif hasattr(item, "text") and item.text:
                text_parts.append(item.text)
            # Для сложных элементов просто игнорируем, чтобы избежать двойной вложенности
        return " ".join(filter(None, text_parts))

    def _parse_paragraph(self, element: ET.Element) -> Paragraph:
        """Парсит тег <p> с полной обработкой всех FB2 элементов форматирования и вложенности."""
        content = self._parse_paragraph_content(element)
        paragraph_id = element.get("id")  # Извлекаем атрибут id если есть
        return Paragraph(content=content, id=paragraph_id)

    def _parse_poem(self, element: ET.Element) -> Poem:
        """Парсит тег <poem>."""
        stanzas = []
        for stanza_elem in element.findall("stanza"):
            lines = [PoemVerse(text=self._get_full_text_content(v)) for v in stanza_elem.findall("v")]
            stanzas.append(PoemStanza(lines=lines))

        return Poem(
            stanzas=stanzas,
            title=self._get_text(element.find("title/p")),
            text_author=self._get_text(element.find("text-author")),
        )

    def _parse_cite(self, element: ET.Element) -> Cite:
        """Парсит тег <cite>."""
        paragraphs = []
        for child in element:
            if child.tag == "p":
                paragraphs.append(self._parse_paragraph(child))
            elif child.tag == "text-author":
                # Обрабатываем text-author как обычный параграф
                text_content = self._get_text(child)
                if text_content:
                    # Создаем параграф с текстом автора
                    from .fb2_model import Paragraph

                    author_paragraph = Paragraph(content=[f" — {text_content}"])
                    paragraphs.append(author_paragraph)

        return Cite(
            paragraphs=paragraphs,
            text_author=None,  # Больше не используем отдельное поле
        )

    def _parse_subtitle(self, element: ET.Element) -> Subtitle:
        """Парсит тег <subtitle>."""
        from .fb2_model import Subtitle

        content: list[str] = []

        # Обрабатываем все дочерние элементы subtitle
        for child in element:
            if child.tag == "strong":
                # Для элементов форматирования сохраняем только текст
                text = self._get_full_text_content(child)
                if text:
                    content.append(text)
            # Для других тегов тоже берем текст
            else:
                text = self._get_full_text_content(child)
                if text:
                    content.append(text)

        # Если нет дочерних элементов, берем прямой текст
        if not content and element.text:
            content.append(element.text.strip())

        return Subtitle(content=content)

    def _parse_document_info(self, element: Optional[ET.Element]) -> Optional[DocumentInfo]:
        # Реализация парсинга <document-info> (по аналогии с title-info)
        if element is None:
            return None
        return DocumentInfo(
            authors=[self._parse_author(a) for a in element.findall("author")],
            program_used=self._get_text(element.find("program-used")),
            date=self._parse_date(element.find("date")),
            src_urls=[self._get_text(u) or "" for u in element.findall("src-url")],
            doc_id=self._get_text(element.find("id")),
            version=self._get_text(element.find("version")),
        )

    def _parse_publish_info(self, element: Optional[ET.Element]) -> Optional[PublishInfo]:
        # Реализация парсинга <publish-info>
        if element is None:
            return None
        year_str = self._get_text(element.find("year"))
        return PublishInfo(
            book_name=self._get_text(element.find("book-name")),
            publisher=self._get_text(element.find("publisher")),
            city=self._get_text(element.find("city")),
            year=int(year_str) if year_str and year_str.isdigit() else None,
            isbn=self._get_text(element.find("isbn")),
        )

    def _parse_body(self, element: ET.Element) -> Body:
        """Парсит тег <body>."""
        sections = element.findall("section")
        self.logger.debug(f"🔍 Body парсинг: найдено {len(sections)} секций, всего дочерних элементов: {len(element)}")

        # Если есть параграфы на уровне body (из-за поврежденного XML),
        # создаем виртуальную секцию для них
        orphan_paragraphs = []
        for child in element:
            if child.tag == "p":
                orphan_paragraphs.append(child)

        if orphan_paragraphs:
            self.logger.warning(
                f"🔧 Найдено {len(orphan_paragraphs)} параграфов на уровне body, создаем виртуальную секцию"
            )

            # Создаем виртуальный элемент section для orphan параграфов
            virtual_section = ET.Element("section")
            for p in orphan_paragraphs:
                virtual_section.append(p)

            # Добавляем виртуальную секцию к обычным секциям
            sections.append(virtual_section)

        return Body(
            name=element.get("name"),
            title=self._parse_annotation(element.find("title")),
            sections=[self._parse_section(s) for s in sections],
        )

    def _parse_section(self, element: ET.Element) -> Section:
        """Рекурсивно парсит тег <section>."""
        content: list[Union[Section, AnnotationElement]] = []

        # Сначала обрабатываем прямых дочерних элементов, которые не являются вложенными секциями
        for child in element:
            if child.tag == "section":
                content.append(self._parse_section(child))  # Рекурсивный вызов
            elif child.tag == "p":
                content.append(self._parse_paragraph(child))
            elif child.tag == "poem":
                content.append(self._parse_poem(child))
            elif child.tag == "cite":
                content.append(self._parse_cite(child))
            elif child.tag == "epigraph":
                content.append(self._parse_cite(child))
            elif child.tag == "subtitle":
                content.append(self._parse_subtitle(child))
            elif child.tag == "image":
                # Картинки полностью игнорируем
                continue
            elif child.tag == "empty-line":
                # Пустые строки игнорируем
                continue
            # Остальные неизвестные теги тоже игнорируем

        return Section(title=self._parse_annotation(element.find("title")), content=content)
