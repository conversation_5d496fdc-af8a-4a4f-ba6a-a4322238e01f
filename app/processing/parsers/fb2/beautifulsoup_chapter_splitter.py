# app/processing/parsers/fb2/beautifulsoup_chapter_splitter.py

"""
BeautifulSoupChapterSplitter - компонент для разбиения глав через BeautifulSoup HTML анализ.

Этот компонент является последним этапом в каскадном алгоритме разбиения на главы.
Применяется когда все предыдущие стратегии (structural, strict, heuristic, deep_split)
не дали достаточного количества глав.

Алгоритм:
1. Преобразует Markdown контент главы в HTML
2. Анализирует HTML структуру через BeautifulSoup
3. Ищет потенциальные заголовки по различным критериям:
   - HTML заголовки (h1-h6)
   - Выделенный текст (strong, b, em, i)
   - Структурные паттерны (div, p с определенными классами)
   - Текстовые паттерны (числа, ключевые слова)
4. Создает новые главы на основе найденных заголовков

Используется как fallback стратегия для сложных случаев, где стандартные
FB2 маркеры не работают, но HTML структура может дать подсказки.
"""

import logging
import re
from dataclasses import dataclass

from app.processing.canonical_model import CanonicalChapter

# Импортируем BeautifulSoup для HTML анализа
try:
    from bs4 import BeautifulSoup, Tag

    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class HTMLChapterMarker:
    """Информация о потенциальном заголовке главы, найденном в HTML."""

    tag_name: str  # Имя HTML тега (h1, h2, strong, etc.)
    text: str  # Текст заголовка
    weight: int  # Вес уверенности (0-100)
    position: int  # Позиция в документе
    context: str  # Контекст вокруг заголовка


class BeautifulSoupChapterSplitter:
    """Компонент для разбиения глав через анализ HTML структуры с BeautifulSoup.

    Применяется как последний этап каскадного алгоритма, когда стандартные
    FB2 стратегии не дали достаточного количества глав.
    """

    def __init__(self, min_chapter_length: int = 200, min_chapters_threshold: int = 2):
        """Инициализирует сплиттер.

        Args:
            min_chapter_length: Минимальная длина главы в символах
            min_chapters_threshold: Минимальное количество глав для успешного разбиения
        """
        self.min_chapter_length = min_chapter_length
        self.min_chapters_threshold = min_chapters_threshold

        # Паттерны для поиска заголовков в тексте
        self._chapter_patterns = [
            re.compile(r"^(глава|часть|пролог|эпилог|финал)\s*(\d+|\w+)?$", re.IGNORECASE),
            re.compile(r"^\d+\.\s*(глава|часть).*$", re.IGNORECASE),
            re.compile(r"^(chapter|part|prologue|epilogue)\s*(\d+|\w+)?$", re.IGNORECASE),
            re.compile(r"^\d+$"),  # Просто числа
            re.compile(r"^[IVX]+$"),  # Римские цифры
        ]

        # Веса для различных типов HTML элементов
        self._tag_weights = {
            "h1": 95,
            "h2": 90,
            "h3": 85,
            "h4": 80,
            "h5": 75,
            "h6": 70,
            "strong": 65,
            "b": 65,
            "em": 60,
            "i": 60,
            "div": 50,
            "p": 45,
            "span": 40,
        }

    def split_chapter_with_beautifulsoup(self, chapter: CanonicalChapter) -> tuple[list[CanonicalChapter], bool]:
        """Пытается разбить главу на подглавы используя BeautifulSoup HTML анализ.

        Args:
            chapter: Глава для разбиения

        Returns:
            Кортеж из (список глав после разбиения, успешность операции)
        """
        if not BS4_AVAILABLE:
            logger.debug("BeautifulSoup недоступен, пропускаем HTML анализ")
            return [chapter], False

        # Получаем Markdown контент главы
        markdown_content = chapter.content_md
        if not markdown_content or len(markdown_content) < self.min_chapter_length * 2:
            logger.debug(f"Глава '{chapter.title}' слишком короткая для BeautifulSoup разбиения")
            return [chapter], False

        try:
            # Преобразуем Markdown в HTML для анализа
            html_content = self._markdown_to_html(markdown_content)

            # Анализируем HTML структуру
            markers = self._analyze_html_structure(html_content)

            if len(markers) < self.min_chapters_threshold:
                logger.debug(f"BeautifulSoup нашел только {len(markers)} потенциальных заголовков")
                return [chapter], False

            # Создаем новые главы на основе найденных маркеров
            new_chapters = self._create_chapters_from_markers(chapter, html_content, markers)

            if len(new_chapters) > 1:
                logger.debug(f"✅ BeautifulSoup разбил главу '{chapter.title}' на {len(new_chapters)} подглав")
                return new_chapters, True
            else:
                return [chapter], False

        except Exception as e:
            logger.warning(f"Ошибка BeautifulSoup анализа для главы '{chapter.title}': {e}")
            return [chapter], False

    def _markdown_to_html(self, markdown_content: str) -> str:
        """Преобразует Markdown в HTML для анализа.

        Простое преобразование основных Markdown элементов в HTML.
        """
        html = markdown_content

        # Заголовки
        html = re.sub(r"^### (.*?)$", r"<h3>\1</h3>", html, flags=re.MULTILINE)
        html = re.sub(r"^## (.*?)$", r"<h2>\1</h2>", html, flags=re.MULTILINE)
        html = re.sub(r"^# (.*?)$", r"<h1>\1</h1>", html, flags=re.MULTILINE)

        # Жирный текст (обрабатываем до курсива, чтобы избежать конфликтов)
        html = re.sub(r"\*\*(.*?)\*\*", r"<strong>\1</strong>", html)

        # Курсив (только одиночные звездочки, не захваченные жирным текстом)
        html = re.sub(r"(?<!\*)\*([^*]+?)\*(?!\*)", r"<em>\1</em>", html)

        # Разбиваем на строки и обрабатываем каждую
        lines = html.split("\n")
        processed_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Если строка уже содержит HTML теги, оставляем как есть
            if re.search(r"<[^>]+>", line):
                processed_lines.append(line)
            else:
                # Обычный текст оборачиваем в параграф
                processed_lines.append(f"<p>{line}</p>")

        return "\n".join(processed_lines)

    def _analyze_html_structure(self, html_content: str) -> list[HTMLChapterMarker]:
        """Анализирует HTML структуру и ищет потенциальные заголовки глав.

        Args:
            html_content: HTML контент для анализа

        Returns:
            Список найденных маркеров заголовков
        """
        try:
            soup = BeautifulSoup(html_content, "html.parser")
        except Exception as e:
            logger.warning(f"Ошибка парсинга HTML через BeautifulSoup: {e}")
            return []

        markers = []
        position = 0

        # Ищем все потенциальные заголовки
        for element in soup.find_all(["h1", "h2", "h3", "h4", "h5", "h6", "strong", "b", "em", "i", "p", "div"]):
            # Проверяем, что элемент является Tag
            if not isinstance(element, Tag):
                continue

            text = self._extract_clean_text(element)
            if not text:
                continue

            # Анализируем текст на предмет заголовка главы
            weight = self._calculate_header_weight(element, text)

            if weight >= 40:  # Минимальный порог для рассмотрения
                context = self._get_element_context(element)
                marker = HTMLChapterMarker(
                    tag_name=element.name, text=text, weight=weight, position=position, context=context
                )
                markers.append(marker)
                logger.debug(f"Найден потенциальный заголовок: '{text}' (вес: {weight}, тег: {element.name})")

            position += 1

        # Сортируем по позиции и фильтруем по весу
        markers.sort(key=lambda m: m.position)

        # Оставляем только сильные маркеры или если их мало - ослабляем критерии
        strong_markers = [m for m in markers if m.weight >= 60]
        if len(strong_markers) >= self.min_chapters_threshold:
            return strong_markers
        else:
            # Ослабляем критерии если сильных маркеров мало
            return [m for m in markers if m.weight >= 45]

    def _extract_clean_text(self, element: Tag) -> str:
        """Извлекает чистый текст из HTML элемента."""
        if not element:
            return ""

        text = element.get_text(strip=True)

        # Ограничиваем длину потенциального заголовка
        if len(text) > 100:
            return ""

        return text

    def _calculate_header_weight(self, element: Tag, text: str) -> int:
        """Вычисляет вес элемента как потенциального заголовка главы.

        Args:
            element: HTML элемент
            text: Текст элемента

        Returns:
            Вес от 0 до 100
        """
        base_weight = self._tag_weights.get(element.name, 0)

        # Бонусы за соответствие паттернам заголовков
        pattern_bonus = 0
        for pattern in self._chapter_patterns:
            if pattern.match(text.strip()):
                pattern_bonus = 30
                break

        # Бонус за короткий текст (заголовки обычно короткие)
        length_bonus = 0
        if len(text) <= 20:
            length_bonus = 10
        elif len(text) <= 50:
            length_bonus = 5

        # Штраф за слишком длинный текст
        length_penalty = 0
        if len(text) > 80:
            length_penalty = 20

        # Бонус за числовые заголовки
        numeric_bonus = 0
        if re.match(r"^\d+$", text.strip()) or re.match(r"^[IVX]+$", text.strip()):
            numeric_bonus = 15

        # Бонус за заголовки с ключевыми словами
        keyword_bonus = 0
        keywords = ["глава", "часть", "пролог", "эпилог", "chapter", "part"]
        if any(keyword in text.lower() for keyword in keywords):
            keyword_bonus = 20

        total_weight = base_weight + pattern_bonus + length_bonus + numeric_bonus + keyword_bonus - length_penalty
        return min(100, max(0, total_weight))

    def _get_element_context(self, element: Tag) -> str:
        """Получает контекст вокруг элемента для дополнительного анализа."""
        context_parts = []

        # Предыдущий элемент
        prev_sibling = element.previous_sibling
        if prev_sibling and hasattr(prev_sibling, "get_text"):
            prev_text = prev_sibling.get_text(strip=True)
            if prev_text:
                context_parts.append(f"prev: {prev_text[:50]}")

        # Следующий элемент
        next_sibling = element.next_sibling
        if next_sibling and hasattr(next_sibling, "get_text"):
            next_text = next_sibling.get_text(strip=True)
            if next_text:
                context_parts.append(f"next: {next_text[:50]}")

        return " | ".join(context_parts)

    def _create_chapters_from_markers(
        self, original_chapter: CanonicalChapter, html_content: str, markers: list[HTMLChapterMarker]
    ) -> list[CanonicalChapter]:
        """Создает новые главы на основе найденных HTML маркеров.

        Args:
            original_chapter: Исходная глава для разбиения
            html_content: HTML контент главы
            markers: Список найденных маркеров заголовков

        Returns:
            Список новых глав
        """
        if not markers:
            return [original_chapter]

        try:
            soup = BeautifulSoup(html_content, "html.parser")
        except Exception as e:
            logger.warning(f"Ошибка повторного парсинга HTML: {e}")
            return [original_chapter]

        chapters = []
        current_content: list[Tag] = []
        current_title = original_chapter.title

        # Получаем все элементы в порядке появления
        all_elements = list(soup.find_all(["h1", "h2", "h3", "h4", "h5", "h6", "strong", "b", "em", "i", "p", "div"]))

        # Создаем карту маркеров по тексту для быстрого поиска
        marker_map = {marker.text: marker for marker in markers}

        for element in all_elements:
            element_text = self._extract_clean_text(element)

            # Проверяем, является ли элемент маркером заголовка
            if element_text in marker_map:
                # Сохраняем предыдущую главу если есть контент
                if current_content:
                    chapter_content = self._elements_to_markdown(current_content)
                    if len(chapter_content.strip()) >= self.min_chapter_length:
                        new_chapter = CanonicalChapter(
                            title=current_title,
                            content_elements=[],  # Пустой список элементов
                            _renderer=None,
                        )
                        # Устанавливаем контент напрямую
                        new_chapter.content_md = chapter_content
                        chapters.append(new_chapter)

                # Начинаем новую главу
                current_title = element_text
                current_content = []
            else:
                # Добавляем элемент к текущей главе
                current_content.append(element)

        # Добавляем последнюю главу
        if current_content:
            chapter_content = self._elements_to_markdown(current_content)
            if len(chapter_content.strip()) >= self.min_chapter_length:
                new_chapter = CanonicalChapter(title=current_title, content_elements=[], _renderer=None)
                new_chapter.content_md = chapter_content
                chapters.append(new_chapter)

        # Если получилось меньше глав чем ожидалось, возвращаем исходную
        if len(chapters) < self.min_chapters_threshold:
            return [original_chapter]

        return chapters

    def _elements_to_markdown(self, elements: list[Tag]) -> str:
        """Преобразует список HTML элементов обратно в Markdown.

        Args:
            elements: Список HTML элементов

        Returns:
            Markdown строка
        """
        markdown_parts = []

        for element in elements:
            if element.name in ["h1", "h2", "h3", "h4", "h5", "h6"]:
                level = int(element.name[1])
                markdown_parts.append(f"{'#' * level} {element.get_text(strip=True)}")
            elif element.name == "strong" or element.name == "b":
                markdown_parts.append(f"**{element.get_text(strip=True)}**")
            elif element.name == "em" or element.name == "i":
                markdown_parts.append(f"*{element.get_text(strip=True)}*")
            elif element.name == "p":
                markdown_parts.append(element.get_text(strip=True))
            elif element.name == "div":
                markdown_parts.append(element.get_text(strip=True))
            else:
                # Для остальных элементов просто извлекаем текст
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(text)

        return "\n\n".join(filter(None, markdown_parts))
