#!/usr/bin/env python3
"""
Тест интеграции BeautifulSoup в пайплайн разбиения на главы.

Проверяет работу BeautifulSoupChapterSplitter и его интеграцию с ChapterAggregator.
"""

import sys
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent))

from app.processing.canonical_model import CanonicalChapter
from app.processing.parsers.fb2.beautifulsoup_chapter_splitter import BeautifulSoupChapterSplitter
from app.processing.parsers.fb2.chapter_aggregator import ChapterAggregator
from app.processing.parsers.fb2.markdown_renderer import MarkdownRenderer


def test_beautifulsoup_splitter():
    """Тестирует BeautifulSoupChapterSplitter на простом примере."""
    print("🧪 Тестируем BeautifulSoupChapterSplitter...")
    
    # Создаем тестовую главу с Markdown контентом
    test_content = """
    Это начало большой главы с несколькими подразделами.
    
    **Глава 1**
    
    Содержимое первой подглавы. Здесь много текста, который должен быть достаточно длинным для прохождения минимального порога длины главы. Добавляем еще текста для надежности.
    
    **Глава 2**
    
    Содержимое второй подглавы. Тоже много текста, чтобы глава была достаточно длинной. Еще немного текста для уверенности в том, что глава пройдет проверку минимальной длины.
    
    **Глава 3**
    
    Содержимое третьей подглавы. И здесь тоже достаточно текста, чтобы глава была валидной по длине. Добавляем дополнительный контент для надежности.
    """
    
    test_chapter = CanonicalChapter(
        title="Тестовая глава",
        content_elements=[],
        _renderer=None
    )
    test_chapter.content_md = test_content.strip()
    
    # Создаем сплиттер
    splitter = BeautifulSoupChapterSplitter(min_chapter_length=100, min_chapters_threshold=2)
    
    # Тестируем разбиение
    result_chapters, success = splitter.split_chapter_with_beautifulsoup(test_chapter)
    
    print(f"Результат разбиения: {success}")
    print(f"Количество глав: {len(result_chapters)}")
    
    for i, chapter in enumerate(result_chapters):
        print(f"Глава {i+1}: '{chapter.title}' (длина: {len(chapter.content_md)} символов)")
        print(f"Содержимое: {chapter.content_md[:100]}...")
        print()
    
    return success and len(result_chapters) > 1


def test_chapter_aggregator_integration():
    """Тестирует интеграцию BeautifulSoup в ChapterAggregator."""
    print("🧪 Тестируем интеграцию с ChapterAggregator...")
    
    # Создаем MarkdownRenderer
    renderer = MarkdownRenderer()
    
    # Создаем ChapterAggregator с низким порогом для тестирования
    aggregator = ChapterAggregator(
        min_chapters_threshold=2,
        min_chapter_length=100,
        markdown_renderer=renderer
    )
    
    # Проверяем, что BeautifulSoup сплиттер инициализирован
    assert hasattr(aggregator, 'beautifulsoup_splitter'), "BeautifulSoup сплиттер не инициализирован"
    assert aggregator.beautifulsoup_splitter is not None, "BeautifulSoup сплиттер равен None"
    
    print("✅ BeautifulSoup сплиттер успешно инициализирован в ChapterAggregator")
    
    # Проверяем, что метод _apply_beautifulsoup_processing существует
    assert hasattr(aggregator, '_apply_beautifulsoup_processing'), "Метод _apply_beautifulsoup_processing не найден"
    
    print("✅ Метод _apply_beautifulsoup_processing найден")
    
    return True


def test_html_analysis():
    """Тестирует анализ HTML структуры."""
    print("🧪 Тестируем HTML анализ...")
    
    splitter = BeautifulSoupChapterSplitter(min_chapter_length=50, min_chapters_threshold=2)
    
    # Тестовый HTML контент
    html_content = """
    <p>Начало документа</p>
    <h2>Первая глава</h2>
    <p>Содержимое первой главы с достаточным количеством текста.</p>
    <strong>Вторая глава</strong>
    <p>Содержимое второй главы тоже с достаточным текстом.</p>
    <h3>Третья глава</h3>
    <p>И содержимое третьей главы с нужным объемом текста.</p>
    """
    
    # Анализируем HTML структуру
    markers = splitter._analyze_html_structure(html_content)
    
    print(f"Найдено маркеров: {len(markers)}")
    for marker in markers:
        print(f"  - {marker.tag_name}: '{marker.text}' (вес: {marker.weight})")
    
    return len(markers) >= 2


def main():
    """Запускает все тесты."""
    print("🚀 Запуск тестов интеграции BeautifulSoup...")
    print("=" * 60)
    
    tests = [
        ("BeautifulSoup Splitter", test_beautifulsoup_splitter),
        ("ChapterAggregator Integration", test_chapter_aggregator_integration),
        ("HTML Analysis", test_html_analysis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result, None))
            status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
            print(f"Результат: {status}")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"Результат: ❌ ОШИБКА - {e}")
    
    # Итоговый отчет
    print("\n" + "=" * 60)
    print("📊 ИТОГОВЫЙ ОТЧЕТ")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result, error in results:
        if result:
            print(f"✅ {test_name}")
            passed += 1
        else:
            print(f"❌ {test_name}" + (f" - {error}" if error else ""))
    
    print(f"\nПройдено: {passed}/{total}")
    
    if passed == total:
        print("🎉 Все тесты пройдены успешно!")
        return 0
    else:
        print("⚠️ Некоторые тесты не пройдены")
        return 1


if __name__ == "__main__":
    sys.exit(main())
