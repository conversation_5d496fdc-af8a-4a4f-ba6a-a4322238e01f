#!/usr/bin/env python3
"""
Анализ FB2 файлов через BeautifulSoup для понимания структуры и контента.
"""

import sys
from pathlib import Path
from bs4 import BeautifulSoup
from app.storage import LocalStorageManager


def analyze_fb2_with_beautifulsoup(archive_path: str, book_filename: str):
    """Анализирует FB2 файл через BeautifulSoup."""
    
    storage_manager = LocalStorageManager()
    
    try:
        # Читаем файл из архива
        book_stream = storage_manager.read_file_from_archive(archive_path, book_filename)
        content = book_stream.read()
        
        # Пробуем разные кодировки
        xml_str = None
        for encoding in ['utf-8', 'cp1251', 'iso-8859-1', 'latin1']:
            try:
                xml_str = content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if not xml_str:
            print(f"❌ {archive_path}::{book_filename} - Не удалось декодировать")
            return
            
        # Парсим через BeautifulSoup
        try:
            soup = BeautifulSoup(xml_str, features="lxml-xml")
        except Exception as e:
            print(f"❌ {archive_path}::{book_filename} - BeautifulSoup ошибка: {e}")
            return
            
        # Анализируем структуру
        title = "Без названия"
        title_elem = soup.find("book-title")
        if title_elem and title_elem.text:
            title = title_elem.text.strip()[:50]
            
        # Считаем главы (sections)
        sections = soup.find_all("section")
        chapters_count = len(sections)
        
        # Считаем параграфы
        paragraphs = soup.find_all("p")
        paragraphs_count = len(paragraphs)
        
        # Считаем общий объем текста
        total_text = ""
        for p in paragraphs:
            if p.text:
                total_text += p.text.strip() + " "
                
        content_length = len(total_text.strip())
        
        # Проверяем есть ли вообще контент
        has_content = content_length > 0
        
        print(f"📖 {archive_path}::{book_filename}")
        print(f"   Название: {title}")
        print(f"   Глав (sections): {chapters_count}")
        print(f"   Параграфов: {paragraphs_count}")
        print(f"   Символов контента: {content_length}")
        print(f"   Есть контент: {'✅' if has_content else '❌'}")
        
        if not has_content:
            print(f"   ⚠️  ФАЙЛ БЕЗ КОНТЕНТА!")
            
        print()
        
    except Exception as e:
        print(f"❌ {archive_path}::{book_filename} - Общая ошибка: {e}")


def main():
    """Анализирует список проблемных файлов."""
    
    # ПОЛНЫЙ список ВСЕХ проблемных файлов
    all_files = [
        # Файлы с 0 контента (7 файлов)
        ("/mnt/storage/books/zip/zip_searchfloor/2000.zip", "1752.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5852.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5853.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/3000.zip", "2466.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/5000.zip", "4518.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/19000.zip", "18614.fb2"),
        ("/mnt/storage/books/zip/zip_searchfloor/20000.zip", "19401.fb2"),

        # Файлы с исправлениями параграфов (23 файла)
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7285.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7828.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7823.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7824.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7822.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7820.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7819.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7840.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5852.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5853.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/3000.zip", "2155.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/7000.zip", "6659.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/7000.zip", "6658.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/7000.zip", "6919.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/1000.zip", "548.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/1000.zip", "550.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8041.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8355.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8357.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8352.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8353.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8354.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/9000.zip", "8361.fb2"),

        # Файлы с структурными исправлениями (18 файлов, убираем дубликаты)
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7828.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7826.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7823.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7824.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7834.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7818.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7822.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7833.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7820.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7825.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7832.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7819.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7839.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7835.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7836.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7830.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7840.fb2"),  # дубликат
        # ("/mnt/storage/books/zip/zip_searchfloor/8000.zip", "7838.fb2"),

        # Ошибки обработки (18 файлов)
        # ("/mnt/storage/books/zip/zip_searchfloor/4000.zip", "3895.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/2000.zip", "1726.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5057.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5089.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5617.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5660.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5676.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5666.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5669.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/6000.zip", "5674.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/14000.zip", "13778.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/7000.zip", "6923.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/17000.zip", "16951.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/17000.zip", "16922.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/5000.zip", "4011.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/5000.zip", "4524.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/19000.zip", "18394.fb2"),
        # ("/mnt/storage/books/zip/zip_searchfloor/15000.zip", "14041.fb2"),
    ]
    
    print("🔍 Анализ проблемных FB2 файлов через BeautifulSoup\n")
    
    for archive_path, book_filename in all_files:
        analyze_fb2_with_beautifulsoup(archive_path, book_filename)


if __name__ == "__main__":
    main()
